"use client";
import { useState, useEffect } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from 'next/link';
import { But<PERSON>, Alert, Typography } from 'antd';
import { GithubOutlined } from '@ant-design/icons';
import GoogleLogo from "@/app/images/loginProvider/google.svg";
import logo from "@/app/images/logo.png";
import Hivechat from "@/app/images/hivechat.svg";
import { getActiveAuthProvides } from '@/app/(auth)/actions';
import SpinLoading from '@/app/components/loading/SpinLoading';
import { extractAndValidateCallbackUrl } from '@/app/utils/callbackUrl';

const { Title, Text } = Typography;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [githubLoading, setGithubLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [authProviders, setAuthProviders] = useState<string[]>([]);
  const [error, setError] = useState("");

  // 提取并验证 callbackUrl
  const callbackUrl = extractAndValidateCallbackUrl(searchParams);



  async function handleGithubLogin() {
    setGithubLoading(true);
    setError("");
    try {
      const response = await signIn("github", {
        redirect: false,
        callbackUrl: callbackUrl,
      });
      if (response?.error) {
        console.log("Github login error:", response.error);
        setError("Github 登录失败，请重试");
      } else if (response?.url) {
        router.push(response.url);
      } else {
        router.push(callbackUrl);
      }
    } catch (error) {
      console.error("Github login error:", error);
      setError("Github 登录失败，请重试");
    } finally {
      setGithubLoading(false);
    }
  }

  async function handleGoogleLogin() {
    setGoogleLoading(true);
    setError("");
    try {
      const response = await signIn("google", {
        redirect: false,
        callbackUrl: callbackUrl,
      });
      if (response?.error) {
        console.log("Google login error:", response.error);
        setError("Google 登录失败，请重试");
      } else if (response?.url) {
        router.push(response.url);
      } else {
        router.push(callbackUrl);
      }
    } catch (error) {
      console.error("Google login error:", error);
      setError("Google 登录失败，请重试");
    } finally {
      setGoogleLoading(false);
    }
  }

  useEffect(() => {
    const fetchSettings = async () => {
      const activeAuthProvides = await getActiveAuthProvides();
      setAuthProviders(activeAuthProvides)
    }
    fetchSettings().then(() => {
      setIsFetching(false);
    });
  }, []);

  if (isFetching) {
    return (
      <main className="h-dvh flex justify-center items-center">
        <SpinLoading />
        <span className='ml-2 text-gray-600'>Loading ...</span>
      </main>
    )
  }

  return (
    <div className="min-h-screen login-page-bg flex">
      {/* 左侧内容区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-20 xl:px-24 ">
        <div className="mx-auto max-w-xl -mt-20">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <Image src={logo} alt="HiveChat logo" width={40} height={40} />
            <Hivechat className="ml-3" alt="HiveChat text" width={180} height={45} />
          </div>

          {/* 主标题 */}
          <h3 className="text-3xl font-bold leading-10 !text-gray-800 mb-6">
            为团队准备的一站式 AI 助手<br />灵活、便捷、免费
          </h3>

          {/* 副标题 */}
          <Text className="text-lg text-gray-600 leading-relaxed">
            支持顶尖人工智能模型、包括 OpenAI、Claude、Gemini、DeepSeek。
          </Text>
        </div>
      </div>

      {/* 右侧登录表单区域 */}
      <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none w-1/2 lg:px-24 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          {/* 移动端Logo */}
          <div className="flex items-center justify-center mb-8 lg:hidden">
            <Image src={logo} alt="HiveChat logo" width={32} height={32} />
            <Hivechat className="ml-2" alt="HiveChat text" width={156} height={39} />
          </div>

          {/* 登录表单 */}
          <div className="bg-white py-8 px-6 login-form-shadow rounded-lg sm:px-10">
            <div className="mb-6">
              <Title level={3} className="!text-2xl !font-semibold !text-gray-900 !mb-2 text-center">
                登录
              </Title>
            </div>

            <div className="space-y-4">
              {/* Google 登录按钮 */}
              <Button
                type="default"
                block
                size="large"
                loading={googleLoading}
                onClick={handleGoogleLogin}
                icon={<GoogleLogo style={{ width: 18, height: 18 }} />}
                className="!h-12 !text-base !font-medium border-gray-300 hover:border-gray-400 login-button-hover"
              >
                使用 Google 登录
              </Button>

              {/* Github 登录按钮 */}
              <Button
                type="default"
                block
                size="large"
                loading={githubLoading}
                onClick={handleGithubLogin}
                icon={<GithubOutlined style={{ fontSize: 18 }} />}
                className="!h-12 !text-base !font-medium border-gray-300 hover:border-gray-400 login-button-hover"
              >
                使用 Github 登录
              </Button>
            </div>

            {/* 错误信息 */}
            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                className="mt-4"
              />
            )}

            {/* 服务条款 */}
            <div className="mt-8 text-center">
              <Text className="text-xs text-gray-400">
                登录代表同意我们的{' '}
                <Link href="/terms" className="text-blue-600 hover:text-blue-500">
                  服务条款
                </Link>
                {' '}和{' '}
                <Link href="/privacy" className="text-blue-600 hover:text-blue-500">
                  隐私政策
                </Link>
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}